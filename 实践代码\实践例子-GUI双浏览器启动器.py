#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage GUI双浏览器启动器 - 简约版
功能：提供图形化界面，一键启动不同端口的浏览器窗口
端口设置：9988和9989
目标网址：闲鱼IM页面 (https://www.goofish.com/im)
特色：简约干净的GUI界面，小巧精炼
"""

import tkinter as tk
from DrissionPage import Chromium, ChromiumOptions
import threading
import time
from win10toast import ToastNotifier

class BrowserLauncher:
    """
    浏览器启动器GUI类
    用于管理双浏览器窗口的启动和控制
    """
    
    def __init__(self):
        """
        初始化GUI界面和相关变量
        """
        print("初始化浏览器启动器GUI...")
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("DrissionPage 双浏览器启动器")
        self.root.geometry("800x500")  # 左右布局需要更宽的窗口
        self.root.resizable(True, True)
        
        # 窗口居中显示
        self.center_window()
        
        # 设置全局字体为微软雅黑
        self.setup_fonts()
        
        # 浏览器对象存储
        self.browser_9988 = None  # 存储9988端口的浏览器对象
        self.browser_9989 = None  # 存储9989端口的浏览器对象
        
        # 浏览器状态标志
        self.browser_9988_status = False  # 9988端口浏览器是否已启动
        self.browser_9989_status = False  # 9989端口浏览器是否已启动

        # 自动刷新功能相关变量
        self.auto_refresh_enabled = False  # 自动刷新功能是否启用
        self.auto_refresh_thread = None  # 自动刷新线程对象

        # 人机验证检测相关变量
        self.verification_detected = False  # 是否检测到人机验证
        self.window_on_top = False  # 窗口是否置顶

        # 初始化Windows 10通知器
        self.toaster = ToastNotifier()
        
        # 创建界面元素
        self.create_widgets()
        
        print("GUI界面初始化完成")
    
    def center_window(self):
        """
        让窗口在屏幕中央显示
        """
        # 更新窗口以获取实际尺寸
        self.root.update_idletasks()
        
        # 获取窗口尺寸
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        
        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 计算居中位置
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        
        # 设置窗口位置
        self.root.geometry(f'800x500+{x}+{y}')
        print("窗口已居中显示")
    
    def setup_fonts(self):
        """
        设置全局微软雅黑字体
        """
        # 设置默认字体为微软雅黑
        default_font = ("Microsoft YaHei", 10)
        self.root.option_add("*Font", default_font)
        print("已设置全局微软雅黑字体")
    
    def create_widgets(self):
        """
        创建所有GUI组件 - 左右分布布局
        """
        print("开始创建GUI组件...")

        # 创建主标题
        title_label = tk.Label(
            self.root,
            text="DrissionPage 双浏览器启动器",
            font=("Microsoft YaHei", 14, "bold")
        )
        title_label.pack(pady=10)

        # 创建说明文字
        desc_label = tk.Label(
            self.root,
            text="点击下方按钮启动不同端口的浏览器窗口，自动打开闲鱼IM页面",
            font=("Microsoft YaHei", 10)
        )
        desc_label.pack(pady=5)

        # 创建主要内容区域 - 左右分布
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # 左侧控制区域
        left_frame = tk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 右侧日志区域
        right_frame = tk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # === 左侧控制区域内容 ===

        # 按钮区域
        button_frame = tk.Frame(left_frame)
        button_frame.pack(pady=10)

        # 创建一键启动按钮
        self.btn_launch_all = tk.Button(
            button_frame,
            text="一键启动双浏览器",
            font=("Microsoft YaHei", 12, "bold"),
            width=20,
            height=2,
            command=self.launch_both_browsers,
            cursor="hand2"
        )
        self.btn_launch_all.pack(pady=5)

        # 创建自动刷新控制按钮
        self.btn_auto_refresh = tk.Button(
            button_frame,
            text="启动自动刷新防验证",
            font=("Microsoft YaHei", 10),
            width=20,
            height=1,
            command=self.toggle_auto_refresh,
            cursor="hand2"
        )
        self.btn_auto_refresh.pack(pady=5)

        # 创建浏览器状态检测按钮
        self.btn_check_status = tk.Button(
            button_frame,
            text="检测浏览器状态",
            font=("Microsoft YaHei", 10),
            width=20,
            command=self.check_browser_status,
            cursor="hand2"
        )
        self.btn_check_status.pack(pady=5)

        # 创建恢复置顶按钮
        self.btn_restore_top = tk.Button(
            button_frame,
            text="恢复正常模式",
            font=("Microsoft YaHei", 10),
            width=20,
            command=self.restore_normal_mode,
            cursor="hand2",
            state=tk.DISABLED  # 初始状态为禁用
        )
        self.btn_restore_top.pack(pady=5)

        # 创建测试通知按钮
        self.btn_test_notification = tk.Button(
            button_frame,
            text="测试系统通知",
            font=("Microsoft YaHei", 10),
            width=20,
            command=self.test_notification,
            cursor="hand2"
        )
        self.btn_test_notification.pack(pady=5)

        # 状态显示区域
        status_frame = tk.Frame(left_frame)
        status_frame.pack(pady=20)

        # 状态区域标题
        status_title = tk.Label(
            status_frame,
            text="浏览器状态",
            font=("Microsoft YaHei", 11, "bold")
        )
        status_title.pack(pady=(0, 10))

        # 9988端口状态标签
        self.status_9988 = tk.Label(
            status_frame,
            text="端口 9988: 未启动",
            font=("Microsoft YaHei", 10)
        )
        self.status_9988.pack(pady=3)

        # 9989端口状态标签
        self.status_9989 = tk.Label(
            status_frame,
            text="端口 9989: 未启动",
            font=("Microsoft YaHei", 10)
        )
        self.status_9989.pack(pady=3)

        # === 右侧日志区域内容 ===

        # 日志标题
        log_title = tk.Label(
            right_frame,
            text="操作日志",
            font=("Microsoft YaHei", 11, "bold")
        )
        log_title.pack(anchor=tk.W, pady=(0, 5))

        # 创建日志文本框和滚动条容器
        log_container = tk.Frame(right_frame)
        log_container.pack(fill=tk.BOTH, expand=True)

        # 日志文本框
        self.log_text = tk.Text(
            log_container,
            font=("Microsoft YaHei", 9),
            wrap=tk.WORD
        )

        # 滚动条
        scrollbar = tk.Scrollbar(
            log_container,
            orient=tk.VERTICAL,
            command=self.log_text.yview
        )
        self.log_text.configure(yscrollcommand=scrollbar.set)

        # 布局日志区域
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        print("GUI组件创建完成")
    
    def log_message(self, message):
        """
        在日志区域显示消息
        """
        # 获取当前时间
        current_time = time.strftime("%H:%M:%S")
        log_entry = f"[{current_time}] {message}\n"
        
        # 在日志文本框中添加消息
        self.log_text.insert(tk.END, log_entry)
        # 自动滚动到最新消息
        self.log_text.see(tk.END)
        # 更新界面
        self.root.update()
        
        # 同时在控制台打印
        print(message)
    
    def create_browser_options(self, port):
        """
        创建指定端口的浏览器配置选项

        参数:
            port (int): 浏览器端口号

        返回:
            ChromiumOptions: 配置好的浏览器选项对象
        """
        self.log_message(f"正在配置端口 {port} 的浏览器选项...")

        # 创建浏览器配置对象
        options = ChromiumOptions()

        # 设置端口号
        options.set_local_port(port)

        # 获取屏幕尺寸，自动计算浏览器窗口大小
        screen_width = self.root.winfo_screenwidth()  # 获取屏幕宽度
        screen_height = self.root.winfo_screenheight()  # 获取屏幕高度

        # 计算浏览器窗口尺寸
        # 宽度：屏幕宽度的一半减去一些边距，让两个浏览器并排显示
        browser_width = (screen_width // 2) - 60  # 每个浏览器占屏幕宽度的一半，减去60像素边距

        # 高度：屏幕高度减去任务栏高度（通常约100像素），实现顶到屏幕上下的效果
        browser_height = screen_height - 100  # 减去任务栏和标题栏的高度

        self.log_message(f"检测到屏幕尺寸: {screen_width}x{screen_height}")
        self.log_message(f"计算出浏览器窗口尺寸: {browser_width}x{browser_height}")

        # 设置浏览器窗口大小：根据屏幕尺寸自动调整
        options.set_argument('--window-size', f'{browser_width},{browser_height}')

        # 设置窗口启动位置（让两个浏览器窗口并排显示，顶到屏幕上边）
        if port == 9988:
            # 第一个浏览器在左侧，位置从屏幕左上角开始
            options.set_argument('--window-position', '0,0')
        else:
            # 第二个浏览器在右侧，位置从屏幕中间开始
            right_position = browser_width + 60  # 左侧浏览器宽度 + 边距
            options.set_argument('--window-position', f'{right_position},0')
        
        # 禁用各种警告和提示信息（保持功能正常）
        options.set_argument('--disable-web-security')  # 禁用web安全性警告
        options.set_argument('--allow-running-insecure-content')  # 允许运行不安全内容
        options.set_argument('--disable-features=VizDisplayCompositor')  # 禁用某些功能警告
        options.set_argument('--disable-infobars')  # 禁用信息栏（包括警告栏）
        options.set_argument('--disable-notifications')  # 禁用通知
        options.set_argument('--no-first-run')  # 禁用首次运行提示
        options.set_argument('--no-default-browser-check')  # 不检查默认浏览器
        options.set_argument('--disable-default-apps')  # 禁用默认应用提示
        options.set_argument('--disable-extensions')  # 禁用扩展程序提示
        options.set_argument('--disable-component-update')  # 禁用组件更新提示
        options.set_argument('--disable-background-timer-throttling')  # 禁用后台定时器限制警告
        options.set_argument('--disable-backgrounding-occluded-windows')  # 禁用背景窗口警告
        options.set_argument('--disable-renderer-backgrounding')  # 禁用渲染器背景警告
        options.set_argument('--disable-translate')  # 禁用翻译提示
        options.set_argument('--disable-sync')  # 禁用同步提示
        options.set_argument('--disable-prompt-on-repost')  # 禁用重新发布提示
        options.set_argument('--disable-client-side-phishing-detection')  # 禁用钓鱼检测提示
        options.set_argument('--disable-hang-monitor')  # 禁用挂起监控提示
        options.set_argument('--disable-popup-blocking')  # 禁用弹窗阻止提示
        options.set_argument('--silent-debugger-extension-api')  # 静默调试器扩展API
        options.set_argument('--disable-dev-shm-usage')  # 禁用/dev/shm的使用警告
        # 注释掉 --no-sandbox 参数，避免显示警告提示
        # options.set_argument('--no-sandbox')  # 禁用沙盒模式警告

        # 专门针对您提到的通知进行禁用
        options.set_argument('--disable-features=TranslateUI')  # 禁用翻译界面
        options.set_argument('--disable-features=Translate')  # 禁用翻译功能
        options.set_argument('--disable-ipc-flooding-protection')  # 禁用IPC洪水保护警告
        options.set_argument('--disable-component-extensions-with-background-pages')  # 禁用后台扩展警告
        options.set_argument('--disable-background-networking')  # 禁用后台网络警告
        options.set_argument('--disable-background-downloads')  # 禁用后台下载警告
        options.set_argument('--disable-add-to-shelf')  # 禁用添加到书架提示
        options.set_argument('--disable-domain-reliability')  # 禁用域名可靠性检查
        options.set_argument('--disable-features=MediaRouter')  # 禁用媒体路由器
        options.set_argument('--disable-print-preview')  # 禁用打印预览
        options.set_argument('--disable-speech-api')  # 禁用语音API
        options.set_argument('--disable-file-system')  # 禁用文件系统API
        options.set_argument('--disable-permissions-api')  # 禁用权限API
        options.set_argument('--disable-presentation-api')  # 禁用演示API
        options.set_argument('--autoplay-policy=no-user-gesture-required')  # 允许自动播放
        options.set_argument('--disable-features=VizDisplayCompositor,TranslateUI,BlinkGenPropertyTrees')  # 禁用多个功能
        options.set_argument('--disable-blink-features=AutomationControlled')  # 禁用自动化控制检测
        options.set_argument('--exclude-switches=enable-automation')  # 排除自动化开关
        options.set_argument('--disable-extensions-http-throttling')  # 禁用扩展HTTP限制
        options.set_argument('--disable-component-cloud-policy')  # 禁用云策略组件
        options.set_argument('--disable-datasaver-prompt')  # 禁用数据节省提示
        options.set_argument('--disable-desktop-notifications')  # 禁用桌面通知
        options.set_argument('--disable-notification-permission-ui')  # 禁用通知权限界面
        options.set_argument('--disable-save-password-bubble')  # 禁用保存密码气泡
        options.set_argument('--disable-session-crashed-bubble')  # 禁用会话崩溃气泡
        options.set_argument('--disable-translate-ranker-service')  # 禁用翻译排名服务
        options.set_argument('--disable-features=ChromeWhatsNewUI')  # 禁用Chrome新功能界面
        options.set_argument('--disable-features=InterestFeedContentSuggestions')  # 禁用内容建议
        options.set_argument('--disable-search-engine-choice-screen')  # 禁用搜索引擎选择屏幕

        # 专门针对 "--no-sandbox" 警告的额外参数
        options.set_argument('--test-type')  # 测试模式，减少警告
        options.set_argument('--disable-gpu-sandbox')  # 禁用GPU沙盒
        options.set_argument('--disable-software-rasterizer')  # 禁用软件光栅化器
        options.set_argument('--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer')  # 禁用显示合成器
        options.set_argument('--disable-logging')  # 禁用日志记录
        options.set_argument('--disable-gl-drawing-for-tests')  # 禁用GL绘制测试
        options.set_argument('--disable-accelerated-2d-canvas')  # 禁用加速2D画布
        options.set_argument('--disable-accelerated-jpeg-decoding')  # 禁用加速JPEG解码
        options.set_argument('--disable-accelerated-mjpeg-decode')  # 禁用加速MJPEG解码
        options.set_argument('--disable-accelerated-video-decode')  # 禁用加速视频解码
        options.set_argument('--disable-accelerated-video-encode')  # 禁用加速视频编码
        options.set_argument('--disable-app-list-dismiss-on-blur')  # 禁用应用列表失焦消失
        options.set_argument('--disable-background-mode')  # 禁用后台模式
        options.set_argument('--disable-renderer-accessibility')  # 禁用渲染器可访问性
        options.set_argument('--disable-threaded-animation')  # 禁用线程动画
        options.set_argument('--disable-threaded-scrolling')  # 禁用线程滚动
        options.set_argument('--disable-in-process-stack-traces')  # 禁用进程内堆栈跟踪
        options.set_argument('--disable-histogram-customizer')  # 禁用直方图定制器
        options.set_argument('--disable-gl-extensions')  # 禁用GL扩展
        options.set_argument('--disable-composited-antialiasing')  # 禁用合成抗锯齿
        options.set_argument('--disable-canvas-aa')  # 禁用画布抗锯齿
        options.set_argument('--disable-3d-apis')  # 禁用3D API
        options.set_argument('--disable-gpu')  # 禁用GPU加速
        options.set_argument('--disable-d3d11')  # 禁用D3D11
        options.set_argument('--disable-d3d9')  # 禁用D3D9
        options.set_argument('--disable-gl-multisampling')  # 禁用GL多重采样
        options.set_argument('--override-plugin-power-saver-for-testing')  # 覆盖插件省电模式
        options.set_argument('--allow-pre-commit-input')  # 允许预提交输入
        options.set_argument('--disable-ipc-flooding-protection')  # 禁用IPC洪水保护
        options.set_argument('--disable-backgrounding-occluded-windows')  # 禁用背景遮挡窗口
        options.set_argument('--disable-features=TranslateUI,BlinkGenPropertyTrees,CalculateNativeWinOcclusion,CertificateTransparencyComponentUpdater')  # 禁用多个功能
        options.set_argument('--flag-switches-begin')  # 标志开关开始
        options.set_argument('--flag-switches-end')  # 标志开关结束

        self.log_message(f"端口 {port} 的浏览器选项配置完成")
        return options
    
    def launch_both_browsers(self):
        """
        一键启动双浏览器（端口9988和9989）
        """
        # 先检测当前浏览器的真实状态
        self.log_message("正在检测浏览器当前状态...")
        self._check_browser_real_status()

        # 检查是否已经启动
        if self.browser_9988_status and self.browser_9989_status:
            self.log_message("两个浏览器已经都启动了，无需重复启动")
            return
        elif self.browser_9988_status:
            self.log_message("端口 9988 的浏览器已启动，只启动端口 9989 的浏览器")
        elif self.browser_9989_status:
            self.log_message("端口 9989 的浏览器已启动，只启动端口 9988 的浏览器")
        else:
            self.log_message("开始一键启动双浏览器...")

        # 禁用按钮，防止重复点击
        self.btn_launch_all.config(state=tk.DISABLED, text="启动中...")

        # 在新线程中启动浏览器
        threading.Thread(target=self._launch_both_browsers_thread, daemon=True).start()
    
    def _launch_both_browsers_thread(self):
        """
        在后台线程中实际执行双浏览器启动操作
        """
        try:
            success_count = 0
            total_count = 0
            
            # 启动端口 9988 的浏览器（如果还未启动）
            if not self.browser_9988_status:
                total_count += 1
                self.log_message("步骤 1/2: 开始启动端口 9988 的浏览器...")
                if self._launch_single_browser(9988):
                    success_count += 1
                    self.browser_9988_status = True
                    self.root.after(0, lambda: self.update_status_9988(True))
                    self.log_message("端口 9988 的浏览器启动成功！")
                else:
                    self.root.after(0, lambda: self.update_status_9988(False))
                    self.log_message("端口 9988 的浏览器启动失败")
            
            # 启动端口 9989 的浏览器（如果还未启动）
            if not self.browser_9989_status:
                total_count += 1
                self.log_message("步骤 2/2: 开始启动端口 9989 的浏览器...")
                if self._launch_single_browser(9989):
                    success_count += 1
                    self.browser_9989_status = True
                    self.root.after(0, lambda: self.update_status_9989(True))
                    self.log_message("端口 9989 的浏览器启动成功！")
                else:
                    self.root.after(0, lambda: self.update_status_9989(False))
                    self.log_message("端口 9989 的浏览器启动失败")
            
            # 总结启动结果
            if total_count == 0:
                self.log_message("两个浏览器都已经在运行中")
            elif success_count == total_count:
                self.log_message(f"所有浏览器启动成功！共启动 {success_count}/{total_count} 个浏览器")
            else:
                self.log_message(f"部分浏览器启动失败：成功 {success_count}/{total_count} 个")
            
        except Exception as e:
            self.log_message(f"一键启动双浏览器出错: {str(e)}")
        
        finally:
            # 恢复按钮状态
            self.root.after(0, lambda: self.btn_launch_all.config(
                state=tk.NORMAL, 
                text="一键启动双浏览器"
            ))
    
    def _launch_single_browser(self, port):
        """
        启动单个浏览器的内部方法
        
        参数:
            port (int): 浏览器端口号
        
        返回:
            bool: 启动是否成功
        """
        try:
            # 创建浏览器配置
            options = self.create_browser_options(port)
            
            # 创建浏览器对象
            self.log_message(f"正在创建端口 {port} 的浏览器对象...")
            browser = Chromium(addr_or_opts=options)
            
            # 获取当前标签页
            tab = browser.latest_tab
            
            # 访问闲鱼IM页面
            self.log_message(f"正在为端口 {port} 的浏览器访问闲鱼IM页面...")
            tab.get("https://www.goofish.com/im")
            
            # 等待页面加载完成
            self.log_message(f"等待端口 {port} 的浏览器页面加载完成...")
            tab.wait.doc_loaded()
            
            # 存储浏览器对象
            if port == 9988:
                self.browser_9988 = browser
            else:
                self.browser_9989 = browser
            
            return True
            
        except Exception as e:
            self.log_message(f"启动端口 {port} 的浏览器失败: {str(e)}")
            return False
    

    
    def update_status_9988(self, success):
        """
        更新端口9988的状态显示
        """
        if success:
            self.status_9988.config(text="端口 9988: 正常运行")
        else:
            self.status_9988.config(text="端口 9988: 未启动")

    def update_status_9989(self, success):
        """
        更新端口9989的状态显示
        """
        if success:
            self.status_9989.config(text="端口 9989: 正常运行")
        else:
            self.status_9989.config(text="端口 9989: 未启动")

    def check_browser_status(self):
        """
        手动检测浏览器状态的按钮功能
        """
        self.log_message("开始手动检测浏览器状态...")
        self._check_browser_real_status()

    def _check_browser_real_status(self):
        """
        检测浏览器的真实运行状态
        """
        # 检测9988端口浏览器
        if self.browser_9988:
            try:
                # 尝试获取浏览器标题来检测是否还在运行
                title = self.browser_9988.latest_tab.title
                self.browser_9988_status = True
                self.log_message(f"端口 9988 浏览器状态：正常运行 (当前页面: {title})")
                self.root.after(0, lambda: self.update_status_9988(True))
            except Exception as e:
                self.browser_9988_status = False
                self.browser_9988 = None
                self.log_message(f"端口 9988 浏览器状态：已关闭或无响应 ({str(e)})")
                self.root.after(0, lambda: self.update_status_9988(False))
        else:
            self.browser_9988_status = False
            self.log_message("端口 9988 浏览器状态：未启动")
            self.root.after(0, lambda: self.update_status_9988(False))

        # 检测9989端口浏览器
        if self.browser_9989:
            try:
                # 尝试获取浏览器标题来检测是否还在运行
                title = self.browser_9989.latest_tab.title
                self.browser_9989_status = True
                self.log_message(f"端口 9989 浏览器状态：正常运行 (当前页面: {title})")
                self.root.after(0, lambda: self.update_status_9989(True))
            except Exception as e:
                self.browser_9989_status = False
                self.browser_9989 = None
                self.log_message(f"端口 9989 浏览器状态：已关闭或无响应 ({str(e)})")
                self.root.after(0, lambda: self.update_status_9989(False))
        else:
            self.browser_9989_status = False
            self.log_message("端口 9989 浏览器状态：未启动")
            self.root.after(0, lambda: self.update_status_9989(False))

    def toggle_auto_refresh(self):
        """
        切换自动刷新功能的开启/关闭状态
        """
        # 先检测浏览器状态
        self._check_browser_real_status()

        if not self.browser_9988_status and not self.browser_9989_status:
            self.log_message("请先启动浏览器后再开启自动刷新功能")
            return

        if self.auto_refresh_enabled:
            # 关闭自动刷新功能
            self.auto_refresh_enabled = False
            self.btn_auto_refresh.config(text="启动自动刷新防验证")
            self.log_message("自动刷新防验证功能已关闭")
        else:
            # 开启自动刷新功能
            self.auto_refresh_enabled = True
            self.btn_auto_refresh.config(text="停止自动刷新防验证")
            self.log_message("自动刷新防验证功能已启动，每30秒刷新一次页面")

            # 启动自动刷新线程
            self.auto_refresh_thread = threading.Thread(
                target=self._auto_refresh_thread,
                daemon=True
            )
            self.auto_refresh_thread.start()

    def _auto_refresh_thread(self):
        """
        自动刷新功能的后台线程
        每隔30秒刷新一次页面，防止出现人机验证
        """
        while self.auto_refresh_enabled:
            try:
                # 等待30秒
                for _ in range(30):
                    if not self.auto_refresh_enabled:
                        return
                    time.sleep(1)

                # 执行页面刷新
                self._perform_page_refresh()

            except Exception as e:
                self.log_message(f"自动刷新功能出错: {str(e)}")
                time.sleep(5)  # 出错后等待5秒再继续

    def _perform_page_refresh(self):
        """
        执行页面刷新操作，并检测人机验证
        """
        try:
            # 先检测浏览器状态
            self._check_browser_real_status()

            # 获取所有可用的浏览器
            browsers = []
            if self.browser_9988_status and self.browser_9988:
                browsers.append(("9988", self.browser_9988))
            if self.browser_9989_status and self.browser_9989:
                browsers.append(("9989", self.browser_9989))

            if not browsers:
                self.log_message("自动刷新失败：没有可用的浏览器")
                return

            # 刷新所有可用的浏览器页面
            for port, browser in browsers:
                try:
                    tab = browser.latest_tab
                    current_url = tab.url

                    self.log_message(f"端口 {port}: 开始刷新页面，当前URL: {current_url}")

                    # 刷新页面
                    tab.refresh()

                    # 等待页面加载完成
                    tab.wait.doc_loaded()

                    self.log_message(f"端口 {port}: 页面刷新完成，等待2秒后检测人机验证")

                    # 等待2秒后检测人机验证
                    time.sleep(2)

                    # 检测人机验证
                    self._check_verification(port, tab)

                except Exception as e:
                    self.log_message(f"端口 {port}: 刷新页面时出错: {str(e)}")
                    # 如果刷新失败，尝试重新访问页面
                    try:
                        tab.get("https://www.goofish.com/im")
                        tab.wait.doc_loaded()
                        self.log_message(f"端口 {port}: 已重新访问闲鱼IM页面")

                        # 等待2秒后检测人机验证
                        time.sleep(2)
                        self._check_verification(port, tab)
                    except:
                        self.log_message(f"端口 {port}: 重新访问页面也失败")

        except Exception as e:
            self.log_message(f"执行页面刷新时出错: {str(e)}")

    def _check_verification(self, port, tab):
        """
        检测页面是否出现人机验证
        """
        try:
            # 查找人机验证元素
            verification_element = tab.ele('.warnning-text', timeout=1)

            if verification_element:
                # 检查元素内容是否包含验证相关文字
                element_text = verification_element.text
                if "请拖动下方滑块完成验证" in element_text or "通过验证以确保正常访问" in element_text:
                    self.log_message(f"端口 {port}: 检测到人机验证！触发窗口置顶提醒")
                    self._trigger_verification_alert()
                    return True

            self.log_message(f"端口 {port}: 未检测到人机验证，页面正常")
            return False

        except Exception as e:
            # 如果查找元素出错（通常是没找到），说明没有验证
            self.log_message(f"端口 {port}: 人机验证检测完成，页面正常")
            return False

    def _trigger_verification_alert(self):
        """
        触发人机验证警报，使用Windows 10通知和窗口置顶
        """
        if not self.verification_detected:
            self.verification_detected = True
            self.window_on_top = True

            # 发送Windows 10系统通知
            try:
                self.toaster.show_toast(
                    "DrissionPage 紧急提醒",
                    "检测到人机验证！请立即处理验证码！\n点击此通知或查看程序窗口。",
                    icon_path=None,
                    duration=15,  # 显示15秒
                    threaded=True  # 在独立线程中显示，不阻塞程序
                )
                self.log_message("已发送Windows 10系统通知")
            except Exception as e:
                self.log_message(f"发送系统通知失败: {str(e)}")

            # 将窗口置顶并聚焦
            self.root.attributes('-topmost', True)
            self.root.lift()
            self.root.focus_force()

            # 启用恢复按钮
            self.btn_restore_top.config(state=tk.NORMAL)

            # 暂停自动刷新功能
            if self.auto_refresh_enabled:
                self.auto_refresh_enabled = False
                self.btn_auto_refresh.config(text="启动自动刷新防验证")
                self.log_message("已暂停自动刷新功能，等待人工处理验证")

            self.log_message("警告：检测到人机验证！已发送系统通知并置顶窗口，请手动完成验证后点击恢复按钮")

    def restore_normal_mode(self):
        """
        恢复正常模式，取消窗口置顶
        """
        self.verification_detected = False
        self.window_on_top = False

        # 取消窗口置顶
        self.root.attributes('-topmost', False)

        # 禁用恢复按钮
        self.btn_restore_top.config(state=tk.DISABLED)

        self.log_message("已恢复正常模式，窗口取消置顶，可以继续使用自动刷新功能")

    def test_notification(self):
        """
        测试Windows 10系统通知功能
        """
        self.log_message("正在测试Windows 10系统通知...")

        try:
            self.toaster.show_toast(
                "DrissionPage 测试通知",
                "这是一个测试通知！\n如果您能看到这个通知，说明通知功能正常工作。",
                icon_path=None,
                duration=10,  # 显示10秒
                threaded=True  # 在独立线程中显示
            )
            self.log_message("测试通知已发送！请查看系统通知区域")
        except Exception as e:
            self.log_message(f"发送测试通知失败: {str(e)}")
    
    def run(self):
        """
        启动GUI主循环
        """
        self.log_message("GUI界面启动完成，等待用户操作...")
        self.log_message("提示：点击按钮启动对应端口的浏览器")

        # 设置窗口关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动tkinter主循环
        self.root.mainloop()

    def on_closing(self):
        """
        窗口关闭时的处理函数
        """
        # 停止自动刷新功能
        if self.auto_refresh_enabled:
            self.auto_refresh_enabled = False
            self.log_message("正在停止自动刷新功能...")

        # 取消窗口置顶状态
        if self.window_on_top:
            self.root.attributes('-topmost', False)

        # 关闭窗口
        self.root.destroy()

def main():
    """
    主函数：创建并运行GUI应用
    """
    print("=" * 80)
    print("DrissionPage GUI双浏览器启动器 - 简约版")
    print("功能：图形化界面启动双浏览器")
    print("端口：9988 和 9989")
    print("目标：闲鱼IM页面 (https://www.goofish.com/im)")
    print("特色：简约干净的GUI界面，小巧精炼")
    print("=" * 80)
    
    try:
        # 创建GUI应用实例
        app = BrowserLauncher()
        
        # 运行GUI应用
        app.run()
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    """
    程序入口点
    """
    main()